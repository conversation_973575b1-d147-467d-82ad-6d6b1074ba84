#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 测试修正后的train.py代码
使用DaYuanTuZ_0.png作为测试图像
"""

import os
import sys
import time

def main():
    """主测试函数"""
    print("🚀 快速测试修正后的train.py代码")
    print("=" * 60)
    
    # 检查测试图像
    test_image = 'DaYuanTuZ_0.png'
    if not os.path.exists(test_image):
        print(f"❌ 测试图像不存在: {test_image}")
        print("💡 请确保DaYuanTuZ_0.png文件在当前目录")
        return
    
    print(f"✅ 找到测试图像: {test_image}")
    
    # 检查YOLO模型
    yolo_models = [
        'yolo11s.pt',
        'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'
    ]
    
    yolo_model = None
    for model_path in yolo_models:
        if os.path.exists(model_path):
            yolo_model = model_path
            print(f"✅ 找到YOLO模型: {model_path}")
            break
    
    if yolo_model is None:
        print(f"❌ 未找到YOLO模型文件")
        print("💡 请确保有以下文件之一:")
        for model in yolo_models:
            print(f"   - {model}")
        return
    
    print("\n" + "=" * 60)
    print("🧪 开始测试...")
    
    # 测试1: 创建自包含模型
    print("\n📦 步骤1: 创建自包含模型")
    cmd1 = f"python train.py --mode create_self_contained --yolo_model {yolo_model}"
    print(f"执行命令: {cmd1}")
    
    start_time = time.time()
    result1 = os.system(cmd1)
    time1 = time.time() - start_time
    
    if result1 == 0:
        print(f"✅ 自包含模型创建成功 (耗时: {time1:.1f}秒)")
    else:
        print(f"❌ 自包含模型创建失败 (返回码: {result1})")
        return
    
    # 检查生成的模型文件
    model_file = 'models/integrated_yolo_ocr_model.pt'
    if os.path.exists(model_file):
        file_size = os.path.getsize(model_file) / (1024 * 1024)  # MB
        print(f"📁 模型文件: {model_file} ({file_size:.1f} MB)")
    else:
        print(f"❌ 模型文件未找到: {model_file}")
        return
    
    # 测试2: 使用整合模型进行预测
    print(f"\n🎯 步骤2: 使用整合模型预测 {test_image}")
    cmd2 = f"python train.py --mode predict --load_model {model_file} --image {test_image}"
    print(f"执行命令: {cmd2}")
    
    start_time = time.time()
    result2 = os.system(cmd2)
    time2 = time.time() - start_time
    
    if result2 == 0:
        print(f"✅ 整合模型预测成功 (耗时: {time2:.1f}秒)")
    else:
        print(f"❌ 整合模型预测失败 (返回码: {result2})")
    
    # 测试3: 使用传统模型进行预测（对比）
    print(f"\n🔄 步骤3: 使用传统模型预测 {test_image} (对比)")
    cmd3 = f"python train.py --mode predict --yolo_model {yolo_model} --image {test_image}"
    print(f"执行命令: {cmd3}")
    
    start_time = time.time()
    result3 = os.system(cmd3)
    time3 = time.time() - start_time
    
    if result3 == 0:
        print(f"✅ 传统模型预测成功 (耗时: {time3:.1f}秒)")
    else:
        print(f"❌ 传统模型预测失败 (返回码: {result3})")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"   📦 自包含模型创建: {'✅ 成功' if result1 == 0 else '❌ 失败'} ({time1:.1f}秒)")
    print(f"   🎯 整合模型预测: {'✅ 成功' if result2 == 0 else '❌ 失败'} ({time2:.1f}秒)")
    print(f"   🔄 传统模型预测: {'✅ 成功' if result3 == 0 else '❌ 失败'} ({time3:.1f}秒)")
    
    if result1 == 0 and result2 == 0:
        print("\n🎉 修正成功! 整合模型可以正常工作")
        print(f"📁 整合模型文件: {model_file}")
        print("💡 现在您可以只使用这一个文件进行预测，不再依赖外部YOLO权重")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
    
    # 检查结果文件
    print(f"\n📂 检查结果文件:")
    result_dirs = ['results', 'test_results', 'legacy_test_results']
    for result_dir in result_dirs:
        if os.path.exists(result_dir):
            files = os.listdir(result_dir)
            if files:
                print(f"   📁 {result_dir}/: {len(files)} 个文件")
                for file in files[:3]:  # 只显示前3个文件
                    print(f"      - {file}")
                if len(files) > 3:
                    print(f"      ... 还有 {len(files)-3} 个文件")

if __name__ == '__main__':
    main()
